import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TransactionListTile extends StatelessWidget {
  const TransactionListTile({
    super.key,
    required this.transaction,
  });

  final TransactionHistory transaction;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        ModalWrapper.showCustomDialog(
          context,
          child: TransactionDetailModal(
            transaction: transaction,
          ),
        );
      },
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.width(10)),
            decoration: BoxDecoration(
              color: AppColors.gray87.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(
                Sizer.radius(30),
              ),
            ),
            child: svgHelper(transaction.type != 'debit'
                ? AppSvgs.arrowDown
                : AppSvgs.arrowUp),
          ),
          const XBox(12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(children: [
                    TextSpan(
                      text: (transaction.type ?? '').capitalizeFirstLetter(),
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral400,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Nohemi',
                      ),
                    ),
                    TextSpan(
                      text: ' (From ${transaction.source ?? ''})',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral200,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Nohemi',
                      ),
                    ),
                  ]),
                ),
                const YBox(6),
                Row(
                  children: [
                    Text(
                      AppUtils.dayWithSuffixMonthAndYear(
                          transaction.createdAt ?? DateTime.now()),
                      //'Jan 28, 2024',
                      style: AppTypography.text12.copyWith(
                        color: AppColors.neutral200,
                      ),
                    ),
                    const XBox(16),
                    Text(
                      AppUtils.convertDateTime(
                          transaction.createdAt ?? DateTime.now()),
                      style: AppTypography.text12.copyWith(
                        color: AppColors.neutral200,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          RichText(
            text: TextSpan(children: [
              TextSpan(
                text: AppConst.naira,
                style: AppTypography.text14.copyWith(
                  color: AppColors.gray32,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: AppUtils.formatNumber(
                    number: double.tryParse(transaction.amount ?? '0') ?? 0),
                style: AppTypography.text14.copyWith(
                  color: AppColors.gray32,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Nohemi',
                ),
              ),
            ]),
          ),
        ],
      ),
    );
  }
}
