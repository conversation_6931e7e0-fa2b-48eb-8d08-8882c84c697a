import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class EmptyListState extends StatelessWidget {
  const EmptyListState({
    super.key,
    required this.text,
    this.height,
    this.onRetry,
  });

  final String text;
  final double? height;
  final Function()? onRetry;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.height(height ?? 500),
      width: Sizer.screenWidth,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              textAlign: TextAlign.center,
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral400,
              ),
            ),
            const YBox(16),
            if (onRetry != null)
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: CustomBtn.solid(
                  text: 'Retry',
                  onTap: onRetry,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
