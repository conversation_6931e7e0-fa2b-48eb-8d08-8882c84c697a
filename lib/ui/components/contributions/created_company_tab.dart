import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CreatedCompanyTab extends ConsumerStatefulWidget {
  const CreatedCompanyTab({super.key});

  @override
  ConsumerState<CreatedCompanyTab> createState() => _CreatedCompanyTabState();
}

class _CreatedCompanyTabState extends ConsumerState<CreatedCompanyTab> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(companyVmodel).getCreatedCompanies();
    });
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    return Expanded(
      child: LoadableContentBuilder(
        isBusy: companyRef.busy(createdCompState),
        items: companyRef.companiesCreatedByMe,
        loadingBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              top: Sizer.height(16),
              bottom: Sizer.height(100),
            ),
            itemBuilder: (ctx, i) {
              return const Skeletonizer(
                enabled: true,
                child: HomeCashBucketCard(),
              );
            },
            separatorBuilder: (_, __) => const YBox(16),
            itemCount: 8,
          );
        },
        emptyBuilder: (context) {
          return CreateOrJoinCompanyWidget(
            onTap: () {
              Navigator.pushNamed(context, RoutePath.startCompanyScreen);
            },
          );
        },
        contentBuilder: (context) {
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              top: Sizer.height(16),
              bottom: Sizer.height(100),
            ),
            itemBuilder: (ctx, i) {
              final c = companyRef.companiesCreatedByMe[i];
              return HomeCashBucketCard(company: c);
            },
            separatorBuilder: (_, __) => const YBox(16),
            itemCount: companyRef.companiesCreatedByMe.length,
          );
        },
      ),
    );
  }
}
