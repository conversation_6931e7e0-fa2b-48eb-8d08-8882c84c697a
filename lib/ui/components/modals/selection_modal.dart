import 'package:equalcash/core/core.dart';

class SelectionModal extends StatefulWidget {
  const SelectionModal({
    super.key,
    this.title,
    this.selectionColor,
    required this.selections,
    this.maxHeight,
  });

  /// Title displayed at the top of the modal
  final String? title;

  /// List of selection options to display
  final List<String> selections;

  /// Color for the selection text
  final Color? selectionColor;

  /// Maximum height for the modal
  /// If content exceeds this height, the list becomes scrollable
  final double? maxHeight;

  @override
  State<SelectionModal> createState() => _SelectionModalState();
}

class _SelectionModalState extends State<SelectionModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(30),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(
            Sizer.radius(24),
          ),
        ),
      ),
      constraints: widget.maxHeight != null
          ? BoxConstraints(maxHeight: widget.maxHeight!)
          : null,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(10),
          Align(
            alignment: Alignment.center,
            child: Container(
              height: Sizer.height(4),
              width: Sizer.width(50),
              decoration: BoxDecoration(
                color: AppColors.pri300.withAlpha(102), // ~0.4 opacity
              ),
            ),
          ),
          const YBox(40),
          Text(
            widget.title ?? 'Select:',
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(20),
          // Wrap ListView in Expanded with SingleChildScrollView when maxHeight is set
          widget.maxHeight != null
              ? Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    padding: EdgeInsets.only(
                      bottom: Sizer.height(50),
                    ),
                    itemCount: widget.selections.length,
                    separatorBuilder: (_, __) => const YBox(6),
                    itemBuilder: (ctx, i) {
                      return SelectionListTile(
                        title: widget.selections[i],
                        textColor: widget.selectionColor,
                        onTap: () {
                          Navigator.pop(context, widget.selections[i]);
                        },
                      );
                    },
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                    bottom: Sizer.height(50),
                  ),
                  itemCount: widget.selections.length,
                  separatorBuilder: (_, __) => const YBox(6),
                  itemBuilder: (ctx, i) {
                    return SelectionListTile(
                      title: widget.selections[i],
                      textColor: widget.selectionColor,
                      onTap: () {
                        Navigator.pop(context, widget.selections[i]);
                      },
                    );
                  },
                ),
        ],
      ),
    );
  }
}

class SelectionListTile extends StatelessWidget {
  const SelectionListTile({
    super.key,
    required this.title,
    this.onTap,
    this.textColor,
  });

  final String title;
  final Function()? onTap;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
        ),
        child: Text(
          title,
          style: AppTypography.text16.copyWith(
            color: textColor ?? AppColors.neutral300,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
