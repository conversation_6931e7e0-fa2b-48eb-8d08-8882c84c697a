import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class InfoActionModal extends StatelessWidget {
  const InfoActionModal({
    super.key,
    required this.title,
    required this.content,
    this.icon,
    this.textBtnText,
    this.btnText,
    this.isLoading = false,
    this.onTapSoldBtn,
    this.onTapTextBtn,
  });

  final String title;
  final String content;
  final String? btnText;
  final String? textBtnText;
  final String? icon;
  final bool isLoading;
  final Function()? onTapSoldBtn;
  final Function()? onTapTextBtn;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Icon(
                Icons.close,
                size: Sizer.radius(28),
              ),
            ),
          ),
          const YBox(40),
          if (icon != null && icon != '') svgHelper(icon ?? AppSvgs.error),
          if (icon != null) const YBox(24),
          Text(
            title,
            style: AppTypography.text16.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(5),
          Text(
            content,
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral200,
              fontWeight: FontWeight.w400,
            ),
          ),
          const YBox(40),
          isLoading
              ? const BtnLoadState()
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomBtn.solid(
                      text: btnText ?? 'Confirm',
                      onTap: onTapSoldBtn,
                    ),
                    if (onTapTextBtn != null) const YBox(16),
                    if (onTapTextBtn != null)
                      InkWell(
                        onTap: onTapTextBtn,
                        child: Text(
                          textBtnText ?? 'Cancel',
                          style: AppTypography.text16.copyWith(
                            color: AppColors.purpleB0,
                          ),
                        ),
                      ),
                  ],
                ),
          const YBox(40),
        ],
      ),
    );
  }
}
