import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ConditionalContributionModal extends ConsumerStatefulWidget {
  const ConditionalContributionModal({
    super.key,
  });

  @override
  ConsumerState<ConditionalContributionModal> createState() =>
      _ConditionalContributionModalState();
}

class _ConditionalContributionModalState
    extends ConsumerState<ConditionalContributionModal> {
  final noteC = TextEditingController();
  final noteF = FocusNode();

  @override
  void dispose() {
    noteC.dispose();
    noteF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final companyVm = ref.watch(companyVmodel);
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Icon(
                Icons.close,
                size: Sizer.radius(28),
              ),
            ),
          ),
          const YBox(30),
          Text(
            'IK008',
            style: AppTypography.text16.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const YBox(8),
          Text(
            'Add a conditional comment to the Terms & \ncondition of this contribution',
            style: AppTypography.text13.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w400,
              height: 1.5,
            ),
          ),
          const YBox(40),
          CustomTextField(
            controller: noteC,
            focusNode: noteF,
            hintText: 'Add comment',
            maxLines: 4,
            onChanged: (p0) => setState(() {}),
            onSubmitted: (p0) {
              noteF.unfocus();
            },
          ),
          const YBox(40),
          CustomBtn.solid(
            text: 'Submit Condition',
            isLoading: companyVm.busy(acceptRejectCompState),
            online: noteC.text.isNotEmpty,
            onTap: () {
              noteF.unfocus();
              ref
                  .read(companyVmodel)
                  .conditionCompanyInvitation(
                    companyId: 1,
                    note: noteC.text.trim(),
                  )
                  .then((value) {
                handleApiResponse(
                  response: value,
                  onCompleted: () {
                    Navigator.pop(context);
                    ModalWrapper.bottomSheet(
                      context: context,
                      widget: InfoActionModal(
                        title: 'Condition successfully submitted',
                        content:
                            'Declining will take you out of the Unique brothers company',
                        btnText: 'Proceed To Dashboard',
                        onTapSoldBtn: () {
                          Navigator.pushNamedAndRemoveUntil(
                            NavKey.appNavigatorKey.currentContext!,
                            RoutePath.dashboardNavigationScreen,
                            (r) => false,
                          );
                        },
                      ),
                    );
                  },
                );
              });
            },
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
