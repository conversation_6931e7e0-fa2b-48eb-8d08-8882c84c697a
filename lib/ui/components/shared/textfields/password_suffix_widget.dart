import 'package:equalcash/core/core.dart';

class PasswordSuffixWidget extends StatelessWidget {
  final bool showPassword;
  const PasswordSuffixWidget({super.key, required this.showPassword});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(
        top: 11.h,
        bottom: 11.h,
        right: 10.w,
      ),
      decoration: BoxDecoration(
        color: AppColors.accent50,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        showPassword ? 'Hide' : 'Show',
        style: AppTypography.text12.copyWith(
          color: AppColors.primaryPurple,
        ),
      ),
    );
  }
}
