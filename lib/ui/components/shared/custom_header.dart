import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CustomHeader extends StatelessWidget implements PreferredSizeWidget {
  const CustomHeader({
    super.key,
    this.headerText,
    this.leadingIcon,
    this.trailingWidget,
    this.headerTextStyle,
    this.padding,
    this.onTap,
  });

  final String? headerText;
  final Widget? leadingIcon;
  final Widget? trailingWidget;
  final TextStyle? headerTextStyle;
  final EdgeInsetsGeometry? padding;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(50)),
      child: Padding(
        padding: padding ??
            EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  // Check if it can pop
                  onTap: onTap ??
                      () {
                        if (Navigator.canPop(context)) {
                          Navigator.pop(context);
                        }
                      },

                  child: Padding(
                      padding: EdgeInsets.all(Sizer.radius(4)),
                      child: leadingIcon ?? svgHelper(AppSvgs.arrowBack)),
                ),
                if (headerText != null)
                  Padding(
                    padding: EdgeInsets.only(right: Sizer.width(16)),
                    child: Text(
                      headerText ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: headerTextStyle ??
                          AppTypography.text20.copyWith(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: AppColors.neutral400,
                          ),
                    ),
                  ),
                Container(
                  child: trailingWidget,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}

class CustomSubHeader extends StatelessWidget {
  const CustomSubHeader({
    super.key,
    required this.title,
    this.titleSize,
    this.subtitle,
  });

  final String title;
  final double? titleSize;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: AppTypography.text24.copyWith(
            color: AppColors.neutral400,
            fontWeight: FontWeight.w500,
            fontSize: titleSize,
          ),
        ),
        if (subtitle != null) const YBox(6),
        if (subtitle != null)
          Text(
            subtitle ?? '',
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral200,
              height: 1.5,
            ),
          ),
      ],
    );
  }
}

class CustomDashboardAppbar extends StatelessWidget
    implements PreferredSizeWidget {
  const CustomDashboardAppbar({
    super.key,
    this.topWidget,
    this.height,
    this.showSearchField = true,
  });

  final Widget? topWidget;
  final double? height;
  final bool showSearchField;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(height ?? 170)),
      child: Container(
        color: AppColors.primaryPurple,
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(16),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            topWidget ?? const SizedBox.shrink(),
            if (showSearchField && topWidget != null) const YBox(16),
            if (showSearchField)
              CustomTextField(
                // controller: dobC,
                // focusNode: dobF,
                hintText: 'Search contributions...',

                isReadOnly: true,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(Sizer.radius(10)),
                  child: SvgPicture.asset(AppSvgs.search),
                ),
                suffixIcon: const Icon(
                  Iconsax.setting_4,
                  color: AppColors.neutral200,
                ),
                onTap: () {
                  ModalWrapper.bottomSheet(
                    context: context,
                    widget: const SelectionModal(
                      title: 'Select Filter:',
                      selections: [
                        'Cash Bucket',
                        'Cash Drop',
                        'Duration',
                        'Commencement Date',
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height ?? 170);
}
