import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:smile_id/products/enhanceddocv/smile_id_enhanced_document_verification.dart';

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        bottom: false,
        child: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              children: [
                const YBox(70),
                imageHelper(
                  AppImages.onb1,
                  height: Sizer.height(280),
                ),
                const YBox(30),
                Text(
                  "Get Cash at Zero Interest \nRate, for Down Payment \nand as Savings.",
                  textAlign: TextAlign.center,
                  style: AppTypography.text24.copyWith(
                    color: AppColors.neutral400,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () {
                    NavigationHelper.navigateTo(
                      routeName: RoutePath.createAccountScreen,
                    );
                  },
                  text: "Get Started",
                ),
                const YBox(12),
                CustomBtn.solid(
                  onlineColor: AppColors.purpleB0,
                  onTap: () {
                    // NavigationHelper.navigateTo(
                    //   routeName: RoutePath.passwordLoginScreen,
                    // );

                    Navigator.of(context).push(
                      //Requires Navigator.of(context).push in order to load
                      MaterialPageRoute<void>(
                        builder: (BuildContext context) => Scaffold(
                          appBar: AppBar(
                              title: const Text(
                                  "SmileID Enhanced Document Verification")),
                          body: SmileIDEnhancedDocumentVerification(
                            countryCode: "GH",
                            documentType: "DRIVERS_LICENSE",
                            useStrictMode:
                                true, // true if you want to capture using enhanced SmartSelfie™ capture (will invalidate the above)
                            consentGrantedDate: DateTime.now()
                                .toIso8601String(), // date in iso string format for when user granted consent
                            personalDetailsConsentGranted:
                                false, // set true if user has agreed to personal details, will default to false
                            contactInformationConsentGranted:
                                false, // set true if user has agreed to contact information, will default to false
                            documentInformationConsentGranted:
                                false, // set true if user has agreed to document information, will default to false

                            onSuccess: (String? result) {
                              // Your success handling logic
                              final snackBar =
                                  SnackBar(content: Text("Success: $result"));
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);
                              Navigator.of(context)
                                  .pop(); //Return flow to your app
                            },
                            onError: (String errorMessage) {
                              // Your error handling logic
                              final snackBar = SnackBar(
                                  content: Text("Error: $errorMessage"));
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);
                              Navigator.of(context)
                                  .pop(); //Return flow to your app
                            },
                          ),
                        ),
                      ),
                    );
                  },
                  text: "Login",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
