import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class OrderOfCollectionScreen extends ConsumerStatefulWidget {
  const OrderOfCollectionScreen({
    super.key,
    required this.url,
  });

  final String url;

  @override
  ConsumerState<OrderOfCollectionScreen> createState() =>
      _OrderOfCollectionScreenState();
}

class _OrderOfCollectionScreenState
    extends ConsumerState<OrderOfCollectionScreen> {
  bool isSwitchedToTrade = false;

  List<MemberModel> members = [];
  String? companyName;
  String? companyId;
  String? companyType;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initSetup();
    });
  }

  _initSetup() async {
    final r = await ref.read(companyVmodel).getDynamicTypePath(widget.url);
    if (r.success) {
      members = memberModelFromJson(json.encode(r.data["data"]));
      companyName = r.data["extra"]["company_name"];
      companyId = r.data["extra"]["company_id"];
      companyType = r.data["extra"]["company_type"];
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    return BusyOverlay(
      show: companyRef.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: CustomHeader(
          onTap: () {
            if (isSwitchedToTrade) {
              isSwitchedToTrade = false;
              setState(() {});
            } else {
              Navigator.pop(context);
            }
          },
        ),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(20),
            CustomSubHeader(
              title: 'Order Of Collection',
              subtitle: "${companyName ?? ''} (${members.length} Members)",
            ),
            const YBox(24),
            Text(
              'Monthly order of collection',
              style: AppTypography.text18.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(16),
            LoadableContentBuilder(
                isBusy: companyRef.isBusy,
                items: members,
                loadingBuilder: (context) {
                  return const SizedBox.shrink();
                },
                emptyBuilder: (context) {
                  return const EmptyListState(
                    text: 'No members found',
                  );
                },
                contentBuilder: (context) {
                  return ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: members.length,
                    separatorBuilder: (_, __) => const YBox(18),
                    itemBuilder: (ctx, i) {
                      final m = members[i];
                      return CollectionListTile(
                        titleText:
                            "${m.userName ?? ''}${m.isCeo == true ? ' (CEO)' : ''}",
                        optionalTitleText: isSwitchedToTrade
                            ? " ${AppUtils.formatDateToMonthDay(m.collectionDate ?? DateTime.now())}"
                            : null,
                        trailingText: isSwitchedToTrade
                            ? 'Trade'
                            : AppUtils.formatDateToMonthDay(
                                m.collectionDate ?? DateTime.now()),
                        trailingColor: isSwitchedToTrade
                            ? (m.collectionOrderAgreed == true
                                ? AppColors.purpleC9
                                : AppColors.primaryPurple)
                            : null,
                        trailingTap: isSwitchedToTrade
                            ? () {
                                Navigator.pushNamed(
                                  context,
                                  RoutePath.tradeOrderOfCollectionScreen,
                                  arguments: TradeOrderArg(
                                    companyId: companyId ?? '',
                                    member: m,
                                  ),
                                );
                              }
                            : null,
                      );
                    },
                  );
                }),
            YBox(isSwitchedToTrade ? 60 : 32),
            (isSwitchedToTrade)
                ? Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(16),
                      vertical: Sizer.height(8),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.secondary100,
                      borderRadius: BorderRadius.circular(
                        Sizer.radius(8),
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Iconsax.info_circle5,
                          color: AppColors.primaryPurple,
                          size: Sizer.radius(24),
                        ),
                        const XBox(8),
                        Expanded(
                          child: Text(
                            'Stakeholders only have the opportunity of trading three times',
                            style: AppTypography.text13.copyWith(
                              color: AppColors.neutral300,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : Row(
                    children: [
                      Expanded(
                        child: CustomBtn.solid(
                          text: 'Accept',
                          textStyle: AppTypography.text16.copyWith(
                            color: AppColors.white,
                          ),
                          onTap: () async {
                            final r = await ref
                                .read(companyVmodel)
                                .agreeToCollectionOrder(
                                    companyId: companyId ?? '');
                            handleApiResponse(
                              response: r,
                              onCompleted: () {
                                Navigator.pop(context);
                              },
                            );
                          },
                        ),
                      ),
                      const XBox(12),
                      Expanded(
                        child: CustomBtn.solid(
                          text: 'Trade',
                          textStyle: AppTypography.text16.copyWith(
                            color: AppColors.white,
                          ),
                          onlineColor: AppColors.primaryPurple.withOpacity(0.7),
                          onTap: () {
                            if (companyType?.toLowerCase() == 'open') {
                              FlushBarToast.fLSnackBar(
                                  snackBarType: SnackBarType.warning,
                                  message:
                                      'You can only trade in closed companies');
                              return;
                            }
                            isSwitchedToTrade = true;
                            setState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
            const YBox(100),
          ],
        ),
      ),
    );
  }
}

class CollectionListTile extends StatelessWidget {
  const CollectionListTile({
    super.key,
    required this.titleText,
    this.optionalTitleText,
    required this.trailingText,
    this.trailingColor,
    this.trailingTap,
  });

  final String titleText;
  final String? optionalTitleText;
  final String trailingText;
  final Color? trailingColor;
  final VoidCallback? trailingTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              titleText,
              style: AppTypography.text16.copyWith(
                color: AppColors.primaryPurple,
              ),
            ),
            if (optionalTitleText != null && optionalTitleText != '')
              Text(
                optionalTitleText ?? '',
                style: AppTypography.text12.copyWith(
                  color: AppColors.neutral200,
                ),
              ),
          ],
        ),
        const Spacer(),
        InkWell(
          onTap: trailingTap,
          child: Text(
            trailingText,
            style: AppTypography.text14.copyWith(
              color: trailingColor ?? AppColors.neutral300,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
