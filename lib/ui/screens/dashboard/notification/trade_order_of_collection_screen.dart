import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TradeOrderOfCollectionScreen extends ConsumerStatefulWidget {
  const TradeOrderOfCollectionScreen({
    super.key,
    required this.tradeOrderArg,
  });

  final TradeOrderArg tradeOrderArg;

  @override
  ConsumerState<TradeOrderOfCollectionScreen> createState() =>
      _TradeOrderOfCollectionScreenState();
}

class _TradeOrderOfCollectionScreenState
    extends ConsumerState<TradeOrderOfCollectionScreen> {
  final commentC = TextEditingController();
  final commentF = FocusNode();

  bool isSwitchedToTrade = false;

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, commentF);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      commentF.requestFocus();
    });
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    return BusyOverlay(
      show: companyRef.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(20),
            const CustomSubHeader(
              title: 'Trade Order Of Collection',
              subtitle:
                  "You can trade your order of collection with \nsomeone else for a little amount of fee",
            ),
            const YBox(80),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text:
                        'Trade your order of collection with ${widget.tradeOrderArg.member.userName}${widget.tradeOrderArg.member.isCeo == true ? ' (CEO)' : ''} ',
                    style: AppTypography.text18.copyWith(
                      color: AppColors.neutral400,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text:
                        '(${AppUtils.formatDateToMonthDay(widget.tradeOrderArg.member.collectionDate ?? DateTime.now())})',
                    style: AppTypography.text14.copyWith(
                      color: AppColors.neutral200,
                    ),
                  ),
                ],
              ),
            ),
            const YBox(32),
            CustomTextField(
              controller: commentC,
              focusNode: commentF,
              labelText: 'Enter trade details',
              maxLines: 3,
              showLabelHeader: true,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(4),
            Text(
              'Little charge apply',
              style: AppTypography.text13.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(120),
            CustomBtn.solid(
              text: 'Accept',
              online: commentC.text.isNotEmpty,
              textStyle: AppTypography.text16.copyWith(
                color: AppColors.white,
              ),
              onTap: () {
                commentF.unfocus();
                ref
                    .read(companyVmodel)
                    .tradeCollectionOrder(
                      companyId: widget.tradeOrderArg.companyId,
                      targetMemberId: widget.tradeOrderArg.member.id ?? 0,
                      comment: commentC.text.trim(),
                    )
                    .then((value) {
                  handleApiResponse(
                    response: value,
                    onCompleted: () {
                      Navigator.pop(context);
                    },
                  );
                });
              },
            ),
            const YBox(16),
            Text(
              'Stakeholders only have the opportunity of \ntrading three times',
              textAlign: TextAlign.center,
              style: AppTypography.text13.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(100),
          ],
        ),
      ),
    );
  }
}
