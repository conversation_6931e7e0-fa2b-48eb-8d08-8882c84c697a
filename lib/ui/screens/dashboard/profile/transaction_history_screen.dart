import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TransactionHistoryScreen extends ConsumerStatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  ConsumerState<TransactionHistoryScreen> createState() =>
      _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState
    extends ConsumerState<TransactionHistoryScreen> {
  final searchC = TextEditingController();
  final searchF = FocusNode();

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(transactionVmodel).getTransactions();
    });
  }

  @override
  void dispose() {
    searchC.dispose();
    searchF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionRef = ref.watch(transactionVmodel);
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Transaction History',
      ),
      body: LoadableContentBuilder(
        isBusy: transactionRef.isBusy,
        isError: transactionRef.hasError,
        items: const [1, 2, 3, 4],
        // items: transactionRef.transactions,
        loadingBuilder: (context) {
          return const SizerLoader(height: 600);
        },
        errorBuilder: (context) {
          return EmptyListState(
            height: 600,
            text: 'Failed to load company details',
            onRetry: () {
              ref.read(transactionVmodel).getTransactions();
            },
          );
        },
        emptyBuilder: (context) {
          return const EmptyListState(
            height: 600,
            text: 'No transactions found',
          );
        },
        contentBuilder: (context) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            child: Column(
              children: [
                const YBox(24),
                CustomTextField(
                  controller: searchC,
                  focusNode: searchF,
                  hintText: 'Search contributions...',
                  isReadOnly: true,
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(Sizer.radius(10)),
                    child: SvgPicture.asset(AppSvgs.search),
                  ),
                  suffixIcon: InkWell(
                    onTap: () {
                      ModalWrapper.bottomSheet(
                        context: context,
                        widget: const SelectionModal(
                          title: 'Search By:',
                          selections: ['Withdrawals', 'Credit', 'Date'],
                          selectionColor: AppColors.pri400,
                        ),
                      );
                    },
                    child: const Icon(
                      Iconsax.setting_4,
                      color: AppColors.neutral200,
                    ),
                  ),
                ),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      await ref.read(transactionVmodel).getTransactions();
                    },
                    child: ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.only(
                        top: Sizer.height(24),
                        bottom: Sizer.height(100),
                      ),
                      itemBuilder: (ctx, i) {
                        // final t = transactionRef.transactions[i];
                        return TransactionListTile(
                          transaction: TransactionHistory(
                            amount: "1000.00",
                            reference: "EQ-CSH-CONTRIB-UK8qXzOPxAkB7DitNwIM",
                            description:
                                "Contribution deducted for Jos company",
                            status: "success",
                            type: "debit",
                            channel: "paystack",
                            source: "card",
                            destination: " Cash bucket",
                            createdAt: DateTime.now(),
                          ),
                        );
                      },
                      separatorBuilder: (_, __) => const YBox(24),
                      // itemCount: transactionRef.transactions.length,
                      itemCount: 6,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
