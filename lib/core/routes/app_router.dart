import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/screens/screens.dart';
import 'package:page_transition/page_transition.dart';

class AppRouter {
  static Route<dynamic> getRoute(RouteSettings settings) {
    final args = settings.arguments;

    // Route builder with page transitions
    PageTransition buildTransition(Widget widget,
            [PageTransitionType type = PageTransitionType.fade]) =>
        PageTransition(
          child: widget,
          type: type,
          settings: settings,
          duration: const Duration(milliseconds: 300),
        );

    switch (settings.name) {
      case RoutePath.splashScreen:
        return buildTransition(const SplashScreen(), PageTransitionType.fade);
      case RoutePath.onboardingScreen:
        return buildTransition(
            const OnboardingScreen(), PageTransitionType.fade);

      case RoutePath.loginScreen:
        return buildTransition(const LoginScreen());
      case RoutePath.passwordLoginScreen:
        return buildTransition(const PasswordLoginScreen());
      case RoutePath.createAccountScreen:
        return buildTransition(const CreateAccountScreeen());
      case RoutePath.verifyNumberOtpScreen:
        return buildTransition(const VerifyNumberOtpScreen());
      case RoutePath.knowYouBetterScreen:
        return buildTransition(const KnowYouBetterScreen());
      case RoutePath.personalDetailsScreen:
        return buildTransition(const PersonalDetailsScreen());
      case RoutePath.setPasscodeScreen:
        return buildTransition(const SetPasscodeScreen());

      case RoutePath.repeatPasscodeScreen:
        if (args is String) {
          return buildTransition(RepeatPasscodeScreen(passcode: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.enableFaceIdScreen:
        final arg = args as BiometricArg?;
        return buildTransition(EnableFaceIdScreen(args: arg));

      case RoutePath.allowNotificationScreen:
        return buildTransition(const AllowNotificationScreen());
      case RoutePath.accountSuccessScreen:
        return buildTransition(
            const AccountSuccessScreen(), PageTransitionType.fade);
      case RoutePath.forgotPasswordScreen:
        return buildTransition(const ForgotPasswordScreen());

      case RoutePath.otpForgotPasswordScreen:
        if (args is String) {
          return buildTransition(OtpForgotPasswordScreen(phone: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.setNewForgotPasswordScreen:
        return buildTransition(const SetNewForgotPasswordScreen());
      case RoutePath.lockedScreen:
        return buildTransition(const LockedScreen(), PageTransitionType.fade);

      // KYC
      case RoutePath.completeYourProfileScreen:
        return buildTransition(const CompleteYourProfileScreen());
      case RoutePath.verifyYourIdentityScreen:
        return buildTransition(const VerifyYourIdentityScreen());

      // Dashboard
      case RoutePath.dashboardNavigationScreen:
        return buildTransition(
            const DashboardNavigationScreen(), PageTransitionType.fade);
      case RoutePath.startCompanyScreen:
        return buildTransition(const StartCompanyScreen());
      case RoutePath.invitationScreen:
        return buildTransition(const InvitationScreen());

      case RoutePath.contributionDetailsScreen:
        if (args is CompanyModel) {
          return buildTransition(ContributionDetailsScreen(company: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.cmoContributionDetailsScreen:
        if (args is CompanyModel) {
          return buildTransition(CmoContributionDetailsScreen(company: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.welcomeProfileDetailsScreen:
        if (args is CompanyModel) {
          return buildTransition(WelcomeProfileDetailsScreen(company: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.invitationDetailsScreen:
        if (args is String) {
          return buildTransition(InvitationDetailsScreen(url: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.orderOfCollectionScreen:
        if (args is String) {
          return buildTransition(OrderOfCollectionScreen(url: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.tradeOrderOfCollectionScreen:
        if (args is TradeOrderArg) {
          return buildTransition(
              TradeOrderOfCollectionScreen(tradeOrderArg: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      case RoutePath.debitCardScreen:
        return buildTransition(const CardScreen());
      case RoutePath.addCardScreen:
        return buildTransition(const AddCardScreen());
      case RoutePath.addBankAccountScreen:
        return buildTransition(const AddBankAccountScreen());
      case RoutePath.bankAccountScreen:
        return buildTransition(const BankAccountScreen());

      case RoutePath.editProfileScreen:
        return buildTransition(const EditProfileScreen());
      case RoutePath.transactionHistoryScreen:
        return buildTransition(const TransactionHistoryScreen());
      case RoutePath.accountSettingsScreen:
        return buildTransition(const AccountSettingsScreen());
      case RoutePath.helpSupportScreen:
        return buildTransition(const HelpSupportScreen());
      case RoutePath.faqScreen:
        return buildTransition(const FaqScreen());
      case RoutePath.contactScreen:
        return buildTransition(const ContactScreen());
      case RoutePath.changePasswordScreen:
        return buildTransition(const ChangePasswordScreen());
      case RoutePath.changePinScreen:
        return buildTransition(const ChangePinScreen());
      case RoutePath.setNewPinScreen:
        return buildTransition(const SetNewPinScreen());
      case RoutePath.repeatPinScreen:
        return buildTransition(const RepeatPinScreen());

      case RoutePath.customWebviewScreen:
        if (args is WebViewArg) {
          return buildTransition(CustomWebviewScreen(arg: args));
        }
        return errorScreen('Incorrect arguments for ${settings.name}');

      default:
        return errorScreen('No route defined for ${settings.name}');
    }
  }

  static PageTransition errorScreen(String msg) {
    return PageTransition(
      type: PageTransitionType.fade,
      child: Scaffold(
        body: Center(
          child: Text(msg),
        ),
      ),
    );
  }
}
