import 'package:equalcash/core/core.dart';

class TransactionVm extends BaseVm {
  List<TransactionHistory> _transactions = [];
  List<TransactionHistory> get transactions => _transactions;

  Future<ApiResponse> getTransactions() async {
    return await performApiCall(
      url: "/v1/transactions",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _transactions =
            transactionHistoryFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }
}

final transactionVmodel =
    ChangeNotifierProvider<TransactionVm>((ref) => TransactionVm());
