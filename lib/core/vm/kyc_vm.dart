import 'package:equalcash/core/core.dart';

class KycVm extends BaseVm {
  List<VerificationCountriesModel> _verificationCountries = [];
  List<VerificationCountriesModel> get verificationCountries =>
      _verificationCountries;
  Future<ApiResponse> getVerificationCountries() async {
    return await performApiCall(
      url: "/v1/verifications/countries",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _verificationCountries =
            verificationCountriesModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}
