import 'package:equalcash/core/core.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';

class CreateAccountVm extends BaseVm {
  final phoneC = TextEditingController();
  final dialCodeC = TextEditingController();

  final firstNameC = TextEditingController();
  final lastNameC = TextEditingController();
  final emailC = TextEditingController();
  final passwordC = TextEditingController();

  final countryF = FocusNode();
  final phoneF = FocusNode();
  final firstNameF = FocusNode();
  final lastNameF = FocusNode();
  final emailF = FocusNode();
  final passwordF = FocusNode();

  bool _termsIsSelected = false;
  bool get termsIsSelected => _termsIsSelected;
  setTermsIsSelected(bool value) {
    _termsIsSelected = value;
    reBuildUI();
  }

  bool get personalDBtnActive =>
      firstNameC.text.isNotEmpty &&
      lastNameC.text.isNotEmpty &&
      emailC.text.isNotEmpty &&
      passwordC.text.isNotEmpty;

  final countryPicker = const FlCountryCodePicker(
      title: SizedBox.shrink(),
      filteredCountries: ['NG', 'US', 'CA', 'ZA', 'GB']);

  CountryCode selectedCountry =
      const CountryCode(code: 'NG', name: 'Nigeria', dialCode: "+234");

  setSelectedCountry(CountryCode? selectedCountry) {
    this.selectedCountry = selectedCountry ?? this.selectedCountry;
    dialCodeC.text = selectedCountry?.dialCode ?? "+234";

    reBuildUI();
  }

  Future<ApiResponse> createAccount() async {
    return await performApiCall(
      url: "/v1/auth/register",
      method: apiService.post,
      body: {
        "country": selectedCountry.name,
        "phone": "${dialCodeC.text.trim()}${phoneC.text.trim()}",
        "first_name": firstNameC.text,
        "last_name": lastNameC.text,
        "email": emailC.text,
        "password": passwordC.text,
      },
      onSuccess: (data) {
        String token = data["data"]["token"];
        StorageService.storeAccessToken(token);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> requestOTP(
      {VerificationType vType = VerificationType.phone}) async {
    return await performApiCall(
      url: "/v1/auth/identity-verification/request-otp",
      method: apiService.post,
      body: {
        "verification_type": vType.value,
        "recipient": vType == VerificationType.email
            ? emailC.text.trim()
            : "${dialCodeC.text.trim()}${phoneC.text.trim()}",
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyOTP({
    VerificationType vType = VerificationType.phone,
    required String otp,
  }) async {
    return await performApiCall(
      url: "/v1/auth/identity-verification/verify-otp",
      method: apiService.post,
      body: {
        "code": otp,
        "verification_type": vType.value,
        "recipient": vType == VerificationType.email
            ? emailC.text.trim()
            : "${dialCodeC.text.trim()}${phoneC.text.trim()}",
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  @override
  void dispose() {
    printty("CreateAccountVm dispose");

    phoneC.dispose();
    firstNameC.dispose();
    lastNameC.dispose();
    emailC.dispose();
    passwordC.dispose();

    countryF.dispose();
    phoneF.dispose();
    firstNameF.dispose();
    lastNameF.dispose();
    emailF.dispose();
    passwordF.dispose();

    super.dispose();
  }
}

final createAccountVm = ChangeNotifierProvider.autoDispose((ref) {
  return CreateAccountVm();
});
