import 'dart:convert';

List<TransactionHistory> transactionHistoryFromJson(String str) =>
    List<TransactionHistory>.from(
        json.decode(str).map((x) => TransactionHistory.fromJson(x)));

String transactionHistoryToJson(List<TransactionHistory> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TransactionHistory {
  final int? id;
  final String? amount;
  final String? reference;
  final String? description;
  final String? status;
  final String? type;
  final String? channel;
  final String? source;
  final String? destination;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  TransactionHistory({
    this.id,
    this.amount,
    this.reference,
    this.description,
    this.status,
    this.type,
    this.channel,
    this.source,
    this.destination,
    this.createdAt,
    this.updatedAt,
  });

  factory TransactionHistory.fromJson(Map<String, dynamic> json) =>
      TransactionHistory(
        id: json["id"],
        amount: json["amount"],
        reference: json["reference"],
        description: json["description"],
        status: json["status"],
        type: json["type"],
        channel: json["channel"],
        source: json["source"],
        destination: json["destination"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "amount": amount,
        "reference": reference,
        "description": description,
        "status": status,
        "type": type,
        "channel": channel,
        "source": source,
        "destination": destination,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
