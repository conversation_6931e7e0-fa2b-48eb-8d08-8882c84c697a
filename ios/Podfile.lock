PODS:
  - FingerprintJS (1.6.0):
    - FingerprintJS/Core (= 1.6.0)
  - FingerprintJS/Core (1.6.0):
    - FingerprintJS/SystemControl
  - FingerprintJS/SystemControl (1.6.0)
  - Flutter (1.0.0)
  - lottie-ios (4.5.2)
  - permission_handler_apple (9.3.0):
    - Flutter
  - smile_id (11.0.6):
    - Flutter
    - SmileID (= 11.0.2)
  - SmileID (11.0.2):
    - FingerprintJS
    - lottie-ios (~> 4.5.0)
    - SmileIDSecurity (~> 1.0.3)
    - ZIPFoundation (~> 0.9)
  - SmileIDSecurity (1.0.4)
  - ZIPFoundation (0.9.19)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - smile_id (from `.symlinks/plugins/smile_id/ios`)

SPEC REPOS:
  trunk:
    - FingerprintJS
    - lottie-ios
    - SmileID
    - SmileIDSecurity
    - ZIPFoundation

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  smile_id:
    :path: ".symlinks/plugins/smile_id/ios"

SPEC CHECKSUMS:
  FingerprintJS: 3a0c3e7f5035ecae199e5e5836200d9b20f1266a
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  lottie-ios: 96784afc26ea031d3e2b6cae342a4b8915072489
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  smile_id: 602500b200b088a19429e7a1f4d99aaf9be70d50
  SmileID: 907d71f709a12b6dbd951205b70b8eeba89d2cfd
  SmileIDSecurity: 85e1bb1de0849e84b3645379e2bc6d5f2aca1ef4
  ZIPFoundation: b8c29ea7ae353b309bc810586181fd073cb3312c

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.16.2
